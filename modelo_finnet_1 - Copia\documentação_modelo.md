# [Inteli-M3] Modelo para a Finnet 2025

## Modelo que é para ser feito:  

**Qual % de inadimplência previsto para período?**

---

## Contexto
Eu faço Sistemas de Informação no INTELI. Estamos agora no módulo 3 que aborda o seguinte tema abaixo:  

**Projeto 3**  
*Lógica para predição com Inteligência Artificial – Uso de aprendizado de máquina sobre bases de dados para geração de modelos preditivos que auxiliam tomadas de decisão.*  

Sendo assim, como em todo módulo do INTELI nós temos uma empresa parceira que traz um problema para que nós apliquemos o que estamos aprendendo ao longo do módulo para desenvolvermos uma solução.  

Nesse sentido, eu mandei aqui para você todas as informações que foram disponibilizadas pelo parceiro de projeto para que nós apliquemos em uma solução.  

Mandei o TAPI do projeto com o que foi pedido pela empresa.  
A empresa parceira é a **Finnet**.  

Visto esse cenário, nós temos que desenvolver um projeto para a Finnet. Mas nós não somente fazemos o projeto em código, nós também fazemos todo um planejamento e análise de negócios da empresa, do setor e do projeto, e isso é entregue em forma de documentação no repositório do projeto no GitHub.  

Além disso, fazemos análises matemáticas para sustentar toda a documentação do projeto, bem como o código do projeto.  

---

## Artefato de Computação
Nesse cenário irei mandar aqui embaixo o que foi pedido para que fosse feito no artefato de computação do nosso projeto:  

Para fazer o projeto nós estamos utilizando o modelo **CRISP-DM**.  

Para todos os propósitos acima, considere o contexto do meu projeto e os datasets que eu mandarei aqui utilizado pelo grupo.  

Assim, una os datasets:  
- Grupo com registro entre 07-2024 a 06-2025- GL  
- Grupo com registro entre 07-2024 a 06-2025- GM  
- Grupo com registro entre 07-2024 a 06-2025- GP  
- Grupo com registro entre 07-2024 a 06-2025- GT  

Em um só dataset.  

---

## Modelo
Modelo que terá que ser feito é o abaixo:  

**Qual % de inadimplência previsto para período?**  

A partir de um período informado, a solução deve trazer uma projeção da inadimplência para o período.  

---

## Informações base para análise
- data de vencimento da cobrança  
- data de pagamento da cobrança  
- valor original  
- valor pago  

**Inadimplência valor:** total de valores em atraso / total de valores  
**Inadimplência quantidade:** quantidade de títulos em atraso / quantidade de títulos  

---

## Entrega esperada
- Escolha das métricas comparativas dos modelos baseada no que é mais importante para o problema ao se medir a qualidade do modelo;  
- Modelo candidato com tuning de hiperparâmetros e suas respectivas métricas;  
- [em caso de modelo supervisionado] Explicabilidade de modelo (deve permitir explicabilidade)  

---

## Critérios e Pontuação
**Escolha das métricas e justificativa:** até 3 pontos  

**Modelos otimizados:** até 7 pontos  
- Apresentar modelos (mínimo 3) e suas métricas.  
- Em caso de modelos supervisionados, deve apresentar explicabilidade (até 3 pts)  
- O modelo apresentado foi otimizado utilizando algum algoritmo de otimização para os hiperparâmetros?  
  Ex.: Grid Search e Random Search (até 2 pts)  

---

## Site da empresa
[https://finnet.com.br](https://finnet.com.br/?gad_source=1&gad_campaignid=21460685239&gbraid=0AAAAABK4-2UpwwPA-Iqp1B8WN-ca2kuE-&gclid=Cj0KCQjw18bEBhCBARIsAKuAFEZRKqp-oUJwe6hIwF9SZQiejrqhuldVnp9HuEA3yn36gHXLrucKoBUaAilKEALw_wcB)

---

## Entregáveis
- **Notebook Completo:** Um notebook Jupyter documentando todo o processo, desde a exploração dos dados até a criação e avaliação do modelo.  

---

## Critérios de Avaliação das Submissões

### Limpeza e Tratamento de Valores Nulos (até 0,5 pt)
- A qualidade dos dados é crucial. Demonstre seu processo de limpeza, incluindo a maneira como lida com valores ausentes e outliers que possam distorcer os resultados.

### Codificação de Variáveis Categóricas (até 0,5 pt)
- Aplique técnicas apropriadas de codificação para transformar variáveis categóricas em formatos utilizáveis em modelos preditivos, garantindo que a informação essencial não seja perdida no processo.

### Exploração e Visualização dos Dados (até 2,0 pts)
- Realize uma análise exploratória detalhada para descobrir padrões, correlações e tendências nos dados.  
- Use visualizações eficazes para comunicar seus insights e justificar suas escolhas de features e modelos.

### Formulação de Hipóteses (até 1,0 pt)
- Formule três hipóteses que possam explicar os fatores que influenciam o sucesso da empresas.  
- Por exemplo: pode-se investigar se empresas com mais funcionários ou com menos tempo de fundação têm maior chance de sucesso.

### Seleção de Features (até 1,0 pt)
- Escolha as features mais relevantes para o modelo com base em sua análise exploratória e hipóteses formuladas.

### Construção e Avaliação do Modelo (até 2,0 pts)
- Selecione um modelo de machine learning adequado (ou uma combinação de modelos) que maximize a capacidade preditiva.  
- A avaliação deve incluir métricas como acurácia, precisão, recall, e F1-score.

### Finetuning de Hiperparâmetros (até 1,0 pt)
- Realize um ajuste fino (finetuning) dos hiperparâmetros do modelo para otimizar seu desempenho.  
- Detalhe o processo de busca e as justificativas para as escolhas feitas.

### Acurácia Mínima (até 2,0 pts)
- O modelo deve atingir uma acurácia mínima de 80% para ser considerado bem-sucedido (pontuação total).  
- Embora a acurácia seja a métrica principal usada na competição, analise também outras métricas como precisão e recall, para melhor interpretação do desempenho do modelo preditivo treinado.

### Documentação e Apresentação dos Resultados (demérito de até 2,0 pts)
- A documentação clara e a apresentação dos resultados são importantes.  
- O notebook final deve ser bem organizado, com código limpo, e o raciocínio por trás de cada decisão deve ser explicado de forma objetiva e compreensível em células de texto, sem exageros.
